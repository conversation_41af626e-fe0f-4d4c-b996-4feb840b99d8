'use client';

import type { UseFormReturn } from 'react-hook-form';
import type { z } from 'zod';
import React, { useEffect, useMemo, useRef } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import PrescribeSelect from '@/components/ui/prescribe-select';
import { TextInput } from '@/components/ui/text-input';
import { XCircle } from 'lucide-react';
import { useWatch } from 'react-hook-form';

import type { Vials } from '~/components/patient-popup/prescription-utils';
import type { prescriptionSchema } from '~/components/patient-popup/PrescriptionPage';
import type { ProductWithVials } from '~/data/patient-types';
import {
  calculatePricing,
  resolveProductBehavior,
} from '~/components/patient-popup/prescription-utils';

type PrescriptionFormValues = z.infer<typeof prescriptionSchema>;

interface ActivePrescriptionListProps {
  products: ProductWithVials[];
  onClose: () => void;
  form: UseFormReturn<PrescriptionFormValues>;
  index: number;
  product?: ProductWithVials;
  patientHasTreatment: boolean; // Add hasTreatment to props
}

const ActivePrescriptionList = ({
  form,
  products,
  onClose,
  index,
  product,
  patientHasTreatment,
}: ActivePrescriptionListProps) => {
  // Ref to track initial setup and manual user changes
  const initialSetupComplete = useRef(false);
  const initialShortPrescriptionSetupComplete = useRef(false);
  const isUserEditing = useRef(false);

  // Use single useWatch call with proper typing
  const [
    selectedPrescription,
    currentSystem,
    selectedDosage,
    selectedVials,
    refills,
    shortInitialPrescription,
  ] = useWatch({
    control: form.control,
    name: [
      `prescriptions.${index}.prescription`,
      `prescriptions.${index}.refillSystem`,
      `prescriptions.${index}.productPriceId`,
      `prescriptions.${index}.vials`,
      `prescriptions.${index}.refills`,
      `prescriptions.${index}.shortInitialPrescription`,
    ],
  }) as [string, string, string, Vials, string, boolean];

  const selectedProduct = useMemo(
    () => products.find((p) => p.id === selectedPrescription),
    [products, selectedPrescription],
  );

  // Get behavior for the selected product
  const behavior = useMemo(
    () => resolveProductBehavior(selectedProduct),
    [selectedProduct],
  );

  // Memoize prescription options
  const prescriptions = useMemo(() => {
    return [...products]
      .sort((a, b) => Number(a.metadata.order) - Number(b.metadata.order))
      .map((product) => ({
        id: product.id,
        label: product.name,
        value: product.id,
      }));
  }, [products]);

  const {
    dosages,
    systemOptions,
    vialsOptions,
    isSystemDisabled,
    isVialsSelectionDisabled,
    lastDosages,
    isLastDoseDisabled,
    isAdditionalProduct,
    canUseShortInitialPrescription,
    isAcceleratedDosing,
  } = useMemo(() => {
    if (!selectedProduct) {
      return {
        dosages: [],
        systemOptions: [],
        vialsOptions: [],
        isSystemDisabled: true,
        isVialsSelectionDisabled: true,
        lastDosages: [{ id: 'none', label: 'None', value: 'none' }],
        isLastDoseDisabled: true,
        isAdditionalProduct: false,
        canUseShortInitialPrescription: false,
        isAcceleratedDosing: false,
      };
    }

    const isAdditionalProduct = selectedProduct.metadata.type === 'additional';
    const isAcceleratedDosing =
      selectedProduct.metadata.customBehavior === 'accelerated-dosing';
    const dosageList = behavior.getDosageOptions(selectedProduct);
    const currentIsSystemDisabled = behavior.isSystemDisabled(
      selectedProduct,
      dosageList,
    );
    const systemOptionsList = behavior.getSystemOptions(selectedProduct);
    const vialsOptionsList = behavior.getVialsOptions(selectedProduct);
    const lastDosagesList = behavior.getLastDoseOptions(
      selectedProduct,
      selectedDosage,
      currentSystem,
      dosageList,
    );
    const currentIsLastDoseDisabled = behavior.isLastDoseDisabled(
      selectedProduct,
      currentSystem,
      currentIsSystemDisabled,
    );
    const currentCanUseShortInitialPrescription =
      behavior.shouldUseShortInitialPrescription(
        selectedProduct,
        selectedVials,
      );

    return {
      dosages: dosageList,
      systemOptions: systemOptionsList,
      vialsOptions: vialsOptionsList,
      isSystemDisabled: currentIsSystemDisabled,
      isVialsSelectionDisabled:
        behavior.isVialsSelectionDisabled(selectedProduct),
      lastDosages: lastDosagesList,
      isLastDoseDisabled: currentIsLastDoseDisabled,
      isAdditionalProduct,
      canUseShortInitialPrescription: currentCanUseShortInitialPrescription,
      isAcceleratedDosing,
    };
  }, [selectedProduct, selectedDosage, currentSystem, selectedVials, behavior]);

  const totalVials =
    refills && parseInt(refills) > 0
      ? (parseInt(refills) + 1) * parseInt(selectedVials || '1')
      : parseInt(selectedVials || '1');

  const { totalPrice, invoiceAmount } = useMemo(
    () =>
      calculatePricing(selectedProduct, selectedDosage, refills, selectedVials),
    [selectedProduct, selectedDosage, refills, selectedVials],
  );

  // Handle multi-month vials forcing static system
  useEffect(() => {
    if (!selectedVials) return;
    if (!initialSetupComplete.current) return;

    if (selectedVials !== '1') {
      form.setValue(`prescriptions.${index}.refillSystem`, 'static');
      form.setValue(`prescriptions.${index}.finalProductPriceId`, 'none');
    }

    if (!isUserEditing.current) return;

    const isCore =
      (selectedProduct && selectedProduct.metadata.type !== 'additional') ||
      false;

    const shouldCheck =
      isCore && !patientHasTreatment && selectedVials === '1' && index == 0;
    form.setValue(
      `prescriptions.${index}.shortInitialPrescription`,
      shouldCheck,
    );
  }, [selectedVials, form, index, patientHasTreatment, selectedProduct]);

  // Initial setup effect - only runs when prescription changes or on first render with product
  useEffect(() => {
    const defaultVials = isAdditionalProduct
      ? '1'
      : vialsOptions.length > 0
        ? vialsOptions[0]?.value || '1'
        : '1';

    // We need to setup the short initial prescription checkbox
    // even if no product is selected (patient has no desired treatment)
    if (!initialShortPrescriptionSetupComplete.current) {
      // Only auto check the 21-day checkbox if:
      // 1. It's a core product (not additional)
      // 2. The patient hasn't been prescribed yet (hasTreatment is false)
      // 3. The vials is set to 1
      // 4. The prescription is the first one (index == 0)
      // OR isAcceleratedDosing is true ?

      form.setValue(
        `prescriptions.${index}.shortInitialPrescription`,
        isAdditionalProduct
          ? false // Always uncheck for additional/non-core products
          : (!patientHasTreatment && index == 0 && defaultVials == '1') ||
              isAcceleratedDosing, // Use default logic for core products
      );

      initialShortPrescriptionSetupComplete.current = true;
    }

    if (!selectedProduct && !product) return;

    // Determine which product to use for initialization
    const targetProduct = product || selectedProduct;
    if (!targetProduct) return;

    // Is this a prescription change or initial setup?
    console.log
    const isPrescriptionChange =
      selectedProduct &&
      product?.id !== selectedProduct.id &&
      product?.id !== selectedPrescription;

    // Initialize prescription ID if provided a product directly
    if (product && !initialSetupComplete.current) {
      form.setValue(`prescriptions.${index}.prescription`, product.id);
    }

    // On prescription change or initial setup
    if (isPrescriptionChange || !initialSetupComplete.current) {
      // Only reset form values if this is truly a prescription change or initial setup
      // Don't reset if user is actively editing
      if (!isUserEditing.current || isPrescriptionChange) {
        // Set default dosage from product or available options
        const defaultDosage = product
          ? product.defaultPriceId
          : dosages.length === 1
            ? dosages[0]?.value
            : targetProduct.productPrice[0]?.id;

        if (defaultDosage) {
          form.setValue(`prescriptions.${index}.productPriceId`, defaultDosage);
        }

        // Determine appropriate system for this product
        let system = isAdditionalProduct
          ? 'static'
          : isSystemDisabled
            ? 'static'
            : 'scaling';

        // For accelerated dosing products, use the first available system option
        if (
          isAcceleratedDosing &&
          systemOptions.length > 0 &&
          !isSystemDisabled
        ) {
          system = systemOptions[0]?.value || 'scaling';
        }

        form.setValue(`prescriptions.${index}.refillSystem`, system);

        // Only reset final product price if it's not already set from initial values
        // This preserves the initial values set in PrescriptionPage for specific products
        const currentFinalProductPriceId = form.getValues(
          `prescriptions.${index}.finalProductPriceId`,
        );
        console.log(
          `[[[ ActivePrescriptionList: currentFinalProductPriceId for ${JSON.stringify(targetProduct.name)}:`,
          currentFinalProductPriceId,
          'isPrescriptionChange:',
          isPrescriptionChange,
        );
        if (!currentFinalProductPriceId || isPrescriptionChange) {
          console.log(
            `{{{ ActivePrescriptionList: Resetting finalProductPriceId to 'none' for ${JSON.stringify(targetProduct.name)}`,
          );
          form.setValue(`prescriptions.${index}.finalProductPriceId`, 'none');
        } else {
          console.log(
            `{{{ ActivePrescriptionList: Preserving existing finalProductPriceId for ${JSON.stringify(targetProduct.name)}:`,
            currentFinalProductPriceId,
          );
        }
        console.log(']]]');

        // Set vials based on product type
        // Only set default vials if user hasn't made a selection yet
        const currentVials = form.getValues(`prescriptions.${index}.vials`);
        if (!currentVials || isPrescriptionChange) {
          form.setValue(`prescriptions.${index}.vials`, defaultVials);
        }
      }

      initialSetupComplete.current = true;
    }
  }, [product?.id, selectedPrescription, index]);

  useEffect(() => {
    if (!selectedProduct) return;
    if (selectedProduct.metadata?.label?.toLowerCase().includes('zepbound')) {
      form.setValue(`prescriptions.${index}.refills`, 12);
    }
  }, [form, index, selectedProduct]);

  return (
    <div className="relative flex flex-col gap-8 bg-gray-50 px-6 py-8">
      <div className="grid w-full grid-cols-12 items-start gap-4">
        <div className="col-span-2">
          <PrescribeSelect
            name={`prescriptions.${index}.prescription`}
            form={form}
            label="Prescription"
            items={prescriptions}
            required
            showErrors={form.formState.isSubmitted}
            onChangeValue={() => {
              // Mark as not user-initiated change
              isUserEditing.current = false;
              // Reset initial setup for new prescription
              initialSetupComplete.current = false;

              // Get the selected product to determine if it's a core or additional product
              const newProductId = form.getValues(
                `prescriptions.${index}.prescription`,
              );
              const newProduct = products.find((p) => p.id === newProductId);

              // If it's not a core product, uncheck the 21-day checkbox immediately
              if (newProduct && newProduct.metadata.type === 'additional') {
                form.setValue(
                  `prescriptions.${index}.shortInitialPrescription`,
                  false,
                );
              }
            }}
          />
        </div>

        {/* Dosage */}
        <div className="col-span-2">
          <PrescribeSelect
            name={`prescriptions.${index}.productPriceId`}
            form={form}
            label="Dosage"
            items={dosages}
            required
            disabled={dosages.length < 2}
            showErrors={form.formState.isSubmitted}
            onChangeValue={() => {
              isUserEditing.current = true;
            }}
          />
        </div>

        {/* Vials */}
        <div className="col-span-2">
          <PrescribeSelect
            name={`prescriptions.${index}.vials`}
            form={form}
            label="Vials"
            items={vialsOptions}
            required
            disabled={isVialsSelectionDisabled}
            showErrors={form.formState.isSubmitted}
            onChangeValue={() => {
              isUserEditing.current = true;
            }}
          />
          {isVialsSelectionDisabled && (
            <input
              type="hidden"
              {...form.register(`prescriptions.${index}.vials`, {
                shouldUnregister: true,
              })}
              value={form.getValues(`prescriptions.${index}.vials`) || '1'}
            />
          )}
        </div>

        {/* Refills */}
        <div className="col-span-1">
          <TextInput
            type="number"
            name={`prescriptions.${index}.refills`}
            label="Refills"
            required
            min={0}
            className="w-full"
            showErrors={form.formState.isSubmitted}
            disabled={!selectedPrescription}
          />
        </div>

        {/* System */}
        <div className="col-span-2">
          <PrescribeSelect
            name={`prescriptions.${index}.refillSystem`}
            form={form}
            label="System"
            items={systemOptions}
            required={!isSystemDisabled}
            disabled={isSystemDisabled || selectedVials !== '1'}
            showErrors={form.formState.isSubmitted}
            onChangeValue={(value) => {
              isUserEditing.current = true;
              if (value === 'static') {
                form.setValue(
                  `prescriptions.${index}.finalProductPriceId`,
                  'none',
                );
              }
            }}
          />
          {isSystemDisabled && (
            <input
              type="hidden"
              {...form.register(`prescriptions.${index}.refillSystem`, {
                shouldUnregister: false,
              })}
              value={
                form.getValues(`prescriptions.${index}.refillSystem`) ||
                'static'
              }
            />
          )}
        </div>

        {/* Last Dose */}
        <div className="col-span-2">
          <PrescribeSelect
            name={`prescriptions.${index}.finalProductPriceId`}
            form={form}
            label="Last Dose"
            items={lastDosages}
            disabled={isLastDoseDisabled}
            placeholder={isLastDoseDisabled ? 'N/A' : 'Select option'}
            showErrors={form.formState.isSubmitted}
            onChangeValue={() => {
              isUserEditing.current = true;
            }}
          />
        </div>

        {/* 21 Day Initial Checkbox */}
        <div className="col-span-1">
          <div className="flex h-full flex-col justify-end">
            <label
              className="invisible mb-2 text-sm font-medium"
              htmlFor={`prescriptions.${index}.shortInitialPrescription`}
            >
              Placeholder
            </label>
            <div className="flex items-center space-x-2">
              <Checkbox
                id={`prescriptions.${index}.shortInitialPrescription`}
                checked={shortInitialPrescription}
                onCheckedChange={(checked: boolean) => {
                  isUserEditing.current = true;
                  form.setValue(
                    `prescriptions.${index}.shortInitialPrescription`,
                    checked,
                  );
                }}
                disabled={
                  !canUseShortInitialPrescription || isAdditionalProduct
                }
              />
              <label
                htmlFor={`prescriptions.${index}.shortInitialPrescription`}
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                21 Day Initial
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Notes field */}
      <TextInput name={`prescriptions.${index}.notes`} label="Doctor notes" />

      {/* Summary information */}
      {selectedPrescription && (
        <div className="flex w-full items-start gap-6 text-sm text-denim">
          <span>
            Total Vials: <span className="font-bold">{totalVials}</span>
          </span>
          <span>
            Total Price:{' '}
            <span className="font-bold">${totalPrice?.toFixed(2)}</span>
          </span>
          <span>
            Refill Price:{' '}
            <span className="font-bold">${invoiceAmount?.toFixed(2)}</span>
          </span>
        </div>
      )}

      {/* Close button */}
      <div
        onClick={onClose}
        className="absolute right-3 top-3 cursor-pointer"
        onKeyDown={undefined}
      >
        <XCircle />
      </div>
    </div>
  );
};

export default ActivePrescriptionList;
